ERROR:D8: com.android.tools.r8.kotlin.H
ERROR:D8: com.android.tools.r8.kotlin.H
ERROR:D8: com.android.tools.r8.kotlin.H

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':launcher:dexBuilderRelease'.
> Could not resolve all files for configuration ':launcher:detachedConfiguration2'.
   > Failed to transform jetified-inmobi-ads-kotlin-10.8.3-runtime.jar to match attributes {artifactType=ext-dex-dexBuilderRelease, org.gradle.libraryelements=jar, org.gradle.usage=java-runtime}.
      > Execution failed for DexingExternalLibArtifactTransform: C:\Users\<USER>\.gradle\caches\transforms-3\6a42782c933bb1eb80f30113a7038465\transformed\jetified-inmobi-ads-kotlin-10.8.3-runtime.jar.
         > Error while dexing.
   > Failed to transform jetified-play-services-measurement-api-22.4.0-runtime.jar to match attributes {artifactType=ext-dex-dexBuilderRelease, org.gradle.libraryelements=jar, org.gradle.usage=java-runtime}.
      > Execution failed for DexingExternalLibArtifactTransform: C:\Users\<USER>\.gradle\caches\transforms-3\6f0adb858851fa5ad5268069394640ce\transformed\jetified-play-services-measurement-api-22.4.0-runtime.jar.
         > Error while dexing.
   > Failed to transform jetified-kotlin-stdlib-1.9.0.jar to match attributes {artifactType=ext-dex-dexBuilderRelease, org.gradle.libraryelements=jar, org.gradle.usage=java-runtime}.
      > Execution failed for DexingExternalLibArtifactTransform: C:\Users\<USER>\.gradle\caches\transforms-3\aae33edb6e792505ad9cf04f19ff7f04\transformed\jetified-kotlin-stdlib-1.9.0.jar.
         > Error while dexing.

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.

* Get more help at https://help.gradle.org

BUILD FAILED in 2m 56s
Picked up JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8

UnityEngine.GUIUtility:ProcessEvent (int,intptr,bool&)
